import { useState, useCallback, useEffect } from 'react';
import { bookmarkEntity, unbookmarkEntity, getBookmarkedEntities } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import { Entity } from '@/types/entity';

interface UseBookmarksReturn {
  bookmarkedEntityIds: Set<string>;
  isBookmarked: (entityId: string) => boolean;
  toggleBookmark: (entityId: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  refreshBookmarks: () => Promise<void>;
}

export const useBookmarks = (): UseBookmarksReturn => {
  const { session } = useAuth();
  const [bookmarkedEntityIds, setBookmarkedEntityIds] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshBookmarks = useCallback(async () => {
    if (!session?.access_token) {
      setBookmarkedEntityIds(new Set());
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch bookmarked entities with proper pagination (max 100 per request)
      let allEntityIds = new Set<string>();
      let currentPage = 1;
      let hasMorePages = true;
      const maxLimit = 100; // Backend maximum limit

      while (hasMorePages) {
        const bookmarksResponse = await getBookmarkedEntities(
          session.access_token,
          currentPage,
          maxLimit
        );

        // Add entity IDs from this page
        bookmarksResponse.data.forEach((entity: Entity) => {
          allEntityIds.add(entity.id);
        });

        // Check if there are more pages
        const { totalPages, currentPage: responsePage } = bookmarksResponse.meta;
        hasMorePages = responsePage < totalPages;
        currentPage++;

        // Safety check to prevent infinite loops
        if (currentPage > 50) { // Max 5000 bookmarks (50 * 100)
          console.warn('Reached maximum page limit for bookmark fetching');
          break;
        }
      }

      setBookmarkedEntityIds(allEntityIds);
    } catch (err) {
      console.error('Failed to fetch bookmarks:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch bookmarks');
    } finally {
      setIsLoading(false);
    }
  }, [session?.access_token]);

  const isBookmarked = useCallback((entityId: string): boolean => {
    return bookmarkedEntityIds.has(entityId);
  }, [bookmarkedEntityIds]);

  const toggleBookmark = useCallback(async (entityId: string): Promise<void> => {
    if (!session?.access_token) {
      throw new Error('You must be signed in to bookmark entities');
    }

    setIsLoading(true);
    setError(null);

    try {
      const wasBookmarked = bookmarkedEntityIds.has(entityId);
      
      if (wasBookmarked) {
        await unbookmarkEntity(entityId, session.access_token);
        setBookmarkedEntityIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(entityId);
          return newSet;
        });
      } else {
        await bookmarkEntity(entityId, session.access_token);
        setBookmarkedEntityIds(prev => new Set(prev).add(entityId));
      }
    } catch (err) {
      console.error('Failed to toggle bookmark:', err);
      setError(err instanceof Error ? err.message : 'Failed to update bookmark');
      throw err; // Re-throw so components can handle the error
    } finally {
      setIsLoading(false);
    }
  }, [session?.access_token, bookmarkedEntityIds]);

  // Load bookmarks when user signs in
  useEffect(() => {
    refreshBookmarks();
  }, [refreshBookmarks]);

  return {
    bookmarkedEntityIds,
    isBookmarked,
    toggleBookmark,
    isLoading,
    error,
    refreshBookmarks,
  };
};
