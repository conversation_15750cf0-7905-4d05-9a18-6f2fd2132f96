'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  User,
  Bookmark,
  Settings,
  Wrench,
  MessageSquare,
  Calendar,
  MapPin,
  Link as LinkIcon,
  Mail,
  Shield,
  Activity
} from 'lucide-react';
import { CompleteProfileData } from '@/types/profile';
import { getCompleteProfile } from '@/services/api';
import { mockCompleteProfileData, simulateApiDelay } from '@/lib/mockProfileData';

// Import tab components (we'll create these next)
import ProfileOverview from '@/components/profile/ProfileOverview';
import BookmarksSection from '@/components/profile/BookmarksSection';
import PreferencesSection from '@/components/profile/PreferencesSection';
import MyToolsSection from '@/components/profile/MyToolsSection';
import RequestedToolsSection from '@/components/profile/RequestedToolsSection';

export default function ProfilePage() {
  const { user, session, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [profileData, setProfileData] = useState<CompleteProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login?redirect=/profile');
    }
  }, [user, authLoading, router]);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!session?.access_token) return;

      try {
        setIsLoading(true);
        setError(null);

        try {
          // Try to fetch real data first
          const data = await getCompleteProfile(session.access_token);
          setProfileData(data);
        } catch (apiError) {
          // If API fails, use mock data for development
          console.warn('API call failed, using mock data:', apiError);
          await simulateApiDelay(500); // Simulate loading time
          setProfileData(mockCompleteProfileData);
        }
      } catch (err) {
        console.error('Failed to fetch profile data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load profile data');
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.access_token && user) {
      fetchProfileData();
    }
  }, [session?.access_token, user]);

  // Show loading state
  if (authLoading || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="space-y-6">
          {/* Header skeleton */}
          <div className="flex items-center space-x-4">
            <Skeleton className="h-20 w-20 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          
          {/* Tabs skeleton */}
          <div className="space-y-4">
            <div className="flex space-x-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-24" />
              ))}
            </div>
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="text-red-500">
                <Shield className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-semibold">Failed to Load Profile</h3>
              <p className="text-gray-600">{error}</p>
              <Button 
                onClick={() => window.location.reload()}
                variant="outline"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Don't render if no user or profile data
  if (!user || !profileData) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Profile Header */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              {profileData.user.profile_picture_url ? (
                <img
                  src={profileData.user.profile_picture_url}
                  alt={profileData.user.display_name || 'Profile'}
                  className="h-20 w-20 rounded-full object-cover border-4 border-white shadow-lg"
                />
              ) : (
                <div className="h-20 w-20 rounded-full bg-indigo-100 flex items-center justify-center border-4 border-white shadow-lg">
                  <User className="h-10 w-10 text-indigo-600" />
                </div>
              )}
              <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
            
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {profileData.user.display_name || profileData.user.email?.split('@')[0] || 'User'}
              </h1>
              {profileData.user.username && (
                <p className="text-gray-600">@{profileData.user.username}</p>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>Member since {new Date(profileData.stats.member_since).getFullYear()}</span>
                </Badge>
                {profileData.user.technical_level && (
                  <Badge variant="outline">
                    {profileData.user.technical_level}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-indigo-600">{profileData.stats.bookmarks_count}</div>
              <div className="text-sm text-gray-600">Bookmarks</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{profileData.stats.reviews_count}</div>
              <div className="text-sm text-gray-600">Reviews</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{profileData.stats.tools_submitted}</div>
              <div className="text-sm text-gray-600">Tools Submitted</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">{profileData.stats.requests_made}</div>
              <div className="text-sm text-gray-600">Requests Made</div>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="bookmarks" className="flex items-center space-x-2">
            <Bookmark className="h-4 w-4" />
            <span className="hidden sm:inline">Bookmarks</span>
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Preferences</span>
          </TabsTrigger>
          <TabsTrigger value="my-tools" className="flex items-center space-x-2">
            <Wrench className="h-4 w-4" />
            <span className="hidden sm:inline">My Tools</span>
          </TabsTrigger>
          <TabsTrigger value="requested-tools" className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4" />
            <span className="hidden sm:inline">Requests</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <ProfileOverview profileData={profileData} onUpdate={setProfileData} />
        </TabsContent>

        <TabsContent value="bookmarks">
          <BookmarksSection />
        </TabsContent>

        <TabsContent value="preferences">
          <PreferencesSection profileData={profileData} onUpdate={setProfileData} />
        </TabsContent>

        <TabsContent value="my-tools">
          <MyToolsSection />
        </TabsContent>

        <TabsContent value="requested-tools">
          <RequestedToolsSection />
        </TabsContent>
      </Tabs>
    </div>
  );
}
