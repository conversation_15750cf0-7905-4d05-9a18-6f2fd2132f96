'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  MessageSquare, 
  Plus, 
  Calendar,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Clock,
  X,
  Send
} from 'lucide-react';
import { ToolRequest, CreateToolRequestPayload } from '@/types/profile';
import { getUserToolRequests, createToolRequest } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import { mockToolRequests, simulateApiDelay } from '@/lib/mockProfileData';

export default function RequestedToolsSection() {
  const { session } = useAuth();
  const [requests, setRequests] = useState<ToolRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showNewRequestForm, setShowNewRequestForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // New request form state
  const [newRequest, setNewRequest] = useState<CreateToolRequestPayload>({
    tool_name: '',
    description: '',
    reason: '',
    category_suggestion: '',
    website_url: '',
    priority: 'medium',
  });

  // Fetch tool requests
  useEffect(() => {
    const fetchRequests = async () => {
      if (!session?.access_token) return;

      try {
        setIsLoading(true);
        setError(null);

        try {
          const response = await getUserToolRequests(session.access_token, 1, 50);
          setRequests(response.data);
        } catch (apiError) {
          // Use mock data if API fails
          console.warn('Tool requests API failed, using mock data:', apiError);
          await simulateApiDelay(300);
          setRequests(mockToolRequests);
        }
      } catch (err) {
        console.error('Failed to fetch tool requests:', err);
        setError(err instanceof Error ? err.message : 'Failed to load tool requests');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRequests();
  }, [session?.access_token]);

  // Handle new request submission
  const handleSubmitRequest = async () => {
    if (!session?.access_token) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const createdRequest = await createToolRequest(newRequest, session.access_token);
      setRequests(prev => [createdRequest, ...prev]);
      
      // Reset form
      setNewRequest({
        tool_name: '',
        description: '',
        reason: '',
        category_suggestion: '',
        website_url: '',
        priority: 'medium',
      });
      setShowNewRequestForm(false);
    } catch (err) {
      console.error('Failed to create tool request:', err);
      setError(err instanceof Error ? err.message : 'Failed to create tool request');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get status badge variant
  const getStatusBadge = (status: ToolRequest['status']) => {
    switch (status) {
      case 'pending':
        return { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' };
      case 'under_review':
        return { variant: 'outline' as const, icon: AlertCircle, color: 'text-blue-600' };
      case 'approved':
        return { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' };
      case 'rejected':
        return { variant: 'destructive' as const, icon: X, color: 'text-red-600' };
      case 'completed':
        return { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' };
      default:
        return { variant: 'secondary' as const, icon: Clock, color: 'text-gray-600' };
    }
  };

  // Get priority badge variant
  const getPriorityBadge = (priority: ToolRequest['priority']) => {
    switch (priority) {
      case 'high':
        return { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' };
      case 'medium':
        return { variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800' };
      case 'low':
        return { variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' };
      default:
        return { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' };
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48 mb-2" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-32 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Tool Requests ({requests.length})
              </CardTitle>
              <CardDescription>
                Track your requests for new AI tools to be added to the platform
              </CardDescription>
            </div>
            
            <Button 
              onClick={() => setShowNewRequestForm(true)}
              disabled={showNewRequestForm}
            >
              <Plus className="h-4 w-4 mr-2" />
              Request New Tool
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* New Request Form */}
          {showNewRequestForm && (
            <Card className="mb-6 border-indigo-200">
              <CardHeader>
                <CardTitle className="text-lg">Request a New Tool</CardTitle>
                <CardDescription>
                  Tell us about the AI tool you'd like to see added to our platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="tool_name">Tool Name *</Label>
                    <Input
                      id="tool_name"
                      value={newRequest.tool_name}
                      onChange={(e) => setNewRequest(prev => ({ ...prev, tool_name: e.target.value }))}
                      placeholder="e.g., ChatGPT, Midjourney"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="website_url">Website URL</Label>
                    <Input
                      id="website_url"
                      value={newRequest.website_url}
                      onChange={(e) => setNewRequest(prev => ({ ...prev, website_url: e.target.value }))}
                      placeholder="https://example.com"
                      type="url"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={newRequest.description}
                    onChange={(e) => setNewRequest(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this tool does and its main features..."
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="reason">Why should we add this tool? *</Label>
                  <Textarea
                    id="reason"
                    value={newRequest.reason}
                    onChange={(e) => setNewRequest(prev => ({ ...prev, reason: e.target.value }))}
                    placeholder="Explain why this tool would be valuable for our community..."
                    rows={2}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="category_suggestion">Suggested Category</Label>
                    <Input
                      id="category_suggestion"
                      value={newRequest.category_suggestion}
                      onChange={(e) => setNewRequest(prev => ({ ...prev, category_suggestion: e.target.value }))}
                      placeholder="e.g., Text Generation, Image Creation"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select 
                      value={newRequest.priority} 
                      onValueChange={(value: any) => setNewRequest(prev => ({ ...prev, priority: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowNewRequestForm(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleSubmitRequest}
                    disabled={isSubmitting || !newRequest.tool_name || !newRequest.description || !newRequest.reason}
                  >
                    {isSubmitting ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Submit Request
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Requests List */}
          {requests.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No tool requests yet</h3>
              <p className="text-gray-600 mb-4">
                Request AI tools you'd like to see added to our platform
              </p>
              <Button onClick={() => setShowNewRequestForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Make Your First Request
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {requests.map((request) => {
                const statusBadge = getStatusBadge(request.status);
                const priorityBadge = getPriorityBadge(request.priority);
                const StatusIcon = statusBadge.icon;

                return (
                  <Card key={request.id} className="border-l-4 border-l-indigo-500">
                    <CardContent className="pt-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {request.tool_name}
                            </h3>
                            {request.website_url && (
                              <a 
                                href={request.website_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-indigo-600 hover:text-indigo-800"
                              >
                                <ExternalLink className="h-4 w-4" />
                              </a>
                            )}
                          </div>
                          <p className="text-gray-600 mb-3">{request.description}</p>
                          <p className="text-sm text-gray-700 mb-3">
                            <strong>Why:</strong> {request.reason}
                          </p>
                        </div>
                        
                        <div className="flex flex-col items-end space-y-2">
                          <Badge variant={statusBadge.variant} className="flex items-center space-x-1">
                            <StatusIcon className={`h-3 w-3 ${statusBadge.color}`} />
                            <span className="capitalize">{request.status.replace('_', ' ')}</span>
                          </Badge>
                          <Badge className={priorityBadge.color}>
                            {request.priority} priority
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>Requested {new Date(request.created_at).toLocaleDateString()}</span>
                          </div>
                          {request.category_suggestion && (
                            <Badge variant="outline" className="text-xs">
                              {request.category_suggestion}
                            </Badge>
                          )}
                        </div>
                        
                        {request.votes > 0 && (
                          <div className="flex items-center space-x-1">
                            <span>{request.votes} votes</span>
                          </div>
                        )}
                      </div>
                      
                      {request.admin_notes && (
                        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                          <p className="text-sm text-blue-800">
                            <strong>Admin Note:</strong> {request.admin_notes}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
